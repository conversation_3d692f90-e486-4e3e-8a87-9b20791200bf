# 技术方案设计

## 架构概述

本次重构将现有的单表结构（chat_history）重构为双表结构（chat_prompt + chat_history），实现提示词与历史记录的分离，提高查询效率和数据管理能力。

## 技术栈

- **数据库**: IndexedDB (通过 Dexie.js)
- **语言**: TypeScript
- **框架**: Chrome Extension
- **数据访问层**: Dexie ORM

## 数据库设计

### 新增表结构

#### chat_prompt 表
```typescript
interface ChatPrompt {
  id: number              // 主键，自增
  chat_prompt: string     // 提示词内容（唯一键）
  chat_uid: string        // 聊天唯一ID
  create_time: number     // 创建时间戳
  is_synced: number       // 是否已同步 (0/1)
  is_delete: number       // 是否删除 (0/1)
}
```

#### 重构后的 chat_history 表
```typescript
interface ChatHistory {
  id: number              // 主键，自增
  chat_answer?: string    // 聊天回答
  chat_uid: string        // 聊天唯一ID（关联chat_prompt）
  platform_id: number    // 平台ID
  tags?: string           // 标签JSON字符串
  chat_group_name?: string // 聊天组名称
  chat_sort?: number      // 聊天序号
  p_uid?: string          // 平台唯一ID
  create_time: number     // 创建时间戳
  is_synced: number       // 是否已同步 (0/1)
  is_answered: number     // 是否有答案 (0/1)
  is_delete: number       // 是否删除 (0/1)
}
```

### 数据库版本升级策略

```mermaid
graph TD
    A[当前版本 v4] --> B[升级到 v5]
    B --> C[创建 chat_prompt 表]
    C --> D[数据迁移]
    D --> E[移除 chat_history.chat_prompt 字段]
    E --> F[更新索引]
    F --> G[验证数据完整性]
```

## 核心组件设计

### 1. 数据库层 (Database Layer)

#### EchoSyncDatabase 扩展
```typescript
export class EchoSyncDatabase extends Dexie {
  chatHistory!: Table<ChatHistory>
  chatPrompt!: Table<ChatPrompt>  // 新增
  platform!: Table<Platform>

  constructor() {
    super('EchoSyncDatabase')
    
    // 版本5：添加chat_prompt表
    this.version(5).stores({
      chatHistory: '++id, chat_uid, platform_id, create_time, is_delete, is_synced, [platform_id+create_time]',
      chatPrompt: '++id, chat_prompt, chat_uid, create_time, is_delete, is_synced', // 新增
      platform: '++id, name, url, is_delete'
    }).upgrade(tx => {
      // 数据迁移逻辑
    })
  }
}
```

### 2. 数据访问层 (DAO Layer)

#### ChatPromptService
```typescript
export class ChatPromptService {
  async create(input: CreateChatPromptInput): Promise<DatabaseResult<ChatPrompt>>
  async findByPrompt(prompt: string): Promise<DatabaseResult<ChatPrompt | null>>
  async getList(params: ChatPromptQueryParams): Promise<DatabaseResult<PaginatedResult<ChatPrompt>>>
  async update(id: number, input: UpdateChatPromptInput): Promise<DatabaseResult<ChatPrompt>>
  async delete(id: number): Promise<DatabaseResult<boolean>>
}
```

#### ChatHistoryService 重构
```typescript
export class ChatHistoryService {
  // 重构创建方法，支持双表操作
  async create(input: CreateChatHistoryInput): Promise<DatabaseResult<{
    chatPrompt: ChatPrompt,
    chatHistory: ChatHistory
  }>>
  
  // 新增：根据chat_uid获取完整信息
  async getFullChatInfo(chatUid: string): Promise<DatabaseResult<{
    prompt: ChatPrompt,
    histories: ChatHistoryWithPlatform[]
  }>>
}
```

### 3. 业务逻辑层 (Service Layer)

#### 数据存储流程
```mermaid
sequenceDiagram
    participant UI as 前端界面
    participant Service as ChatHistoryService
    participant PromptDAO as ChatPromptService
    participant HistoryDAO as ChatHistoryService
    participant DB as IndexedDB

    UI->>Service: 创建聊天记录
    Service->>PromptDAO: 查找已存在的提示词
    alt 提示词不存在
        Service->>PromptDAO: 创建新提示词记录
        PromptDAO->>DB: 插入chat_prompt
    else 提示词已存在
        Service->>Service: 复用existing chat_uid
    end
    Service->>HistoryDAO: 创建历史记录
    HistoryDAO->>DB: 插入chat_history
    Service->>UI: 返回结果
```

#### 查询流程
```mermaid
sequenceDiagram
    participant UI as 前端界面
    participant Service as ChatHistoryService
    participant PromptDAO as ChatPromptService
    participant HistoryDAO as ChatHistoryService
    participant DB as IndexedDB

    UI->>Service: 获取提示词列表
    Service->>PromptDAO: 查询chat_prompt表
    PromptDAO->>DB: SELECT * FROM chat_prompt
    Service->>HistoryDAO: 根据chat_uid批量查询
    HistoryDAO->>DB: SELECT * FROM chat_history WHERE chat_uid IN (...)
    Service->>Service: 组装数据（提示词+平台信息）
    Service->>UI: 返回完整列表
```

## 数据迁移方案

### 迁移步骤

1. **备份现有数据**
   - 导出当前chat_history表所有数据
   - 创建迁移日志记录

2. **创建新表结构**
   - 添加chat_prompt表
   - 更新数据库版本到v5

3. **数据迁移**
   ```typescript
   async migrateData() {
     // 1. 获取所有唯一的chat_prompt
     const uniquePrompts = await this.getUniquePrompts()
     
     // 2. 为每个唯一提示词创建chat_prompt记录
     for (const prompt of uniquePrompts) {
       await this.chatPrompt.add({
         chat_prompt: prompt.content,
         chat_uid: prompt.chat_uid,
         create_time: prompt.earliest_time,
         is_synced: 0,
         is_delete: 0
       })
     }
     
     // 3. 更新chat_history表，移除chat_prompt字段
     // Dexie会自动处理字段移除
   }
   ```

4. **数据验证**
   - 验证迁移后数据完整性
   - 确保关联关系正确
   - 性能测试

## API接口设计

### 新增接口

```typescript
// 获取提示词列表（带平台统计）
interface GetPromptsWithPlatformsResponse {
  prompts: Array<{
    id: number
    chat_prompt: string
    chat_uid: string
    create_time: number
    platforms: Array<{
      platform_id: number
      platform_name: string
      platform_icon: string
      count: number
    }>
  }>
}

// 获取提示词详情
interface GetPromptDetailResponse {
  prompt: ChatPrompt
  histories: ChatHistoryWithPlatform[]
}
```

### 兼容性处理

现有API保持不变，内部实现适配新的数据结构：

```typescript
// 原有的create接口保持签名不变
async create(input: CreateChatHistoryInput): Promise<DatabaseResult<ChatHistory>> {
  // 内部处理双表逻辑
  const result = await this.createWithPrompt(input)
  // 返回兼容格式
  return {
    success: result.success,
    data: result.data?.chatHistory,
    error: result.error
  }
}
```

## 性能优化

### 索引策略
- chat_prompt表：chat_prompt字段唯一索引
- chat_history表：保持现有索引，优化chat_uid查询
- 复合索引：[chat_uid + platform_id] 用于跨平台查询

### 查询优化
- 批量查询：一次性获取多个chat_uid的历史记录
- 缓存策略：常用提示词缓存到内存
- 分页查询：大数据量时使用游标分页

## 风险评估

### 技术风险
- **数据迁移失败**: 通过事务和回滚机制保证
- **性能下降**: 通过索引优化和查询优化解决
- **兼容性问题**: 保持API向后兼容

### 缓解措施
- 完整的单元测试覆盖
- 分阶段发布和灰度测试
- 数据备份和恢复机制
