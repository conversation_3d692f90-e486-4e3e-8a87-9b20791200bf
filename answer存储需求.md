 # answer存储需求
 chat_history中，chat_uid和platform_id组成联合唯一索引，不能重复存储。
 1. 在content页面，如果输入了提示词，点击存档时生成chat_uid存储数据库，这个逻辑不变
 2. 当继续输入或者修改时，如果再次点击存储，则生成新的chat_uid
 3. 如果不存档点击了发送，则使用已存在的chat_uid,在answer回答完毕前，或者取消发送前，本content一直使用当前的chat_uid，再获得答案后，存入当前content对应的platform_id联合chat_uid的那条记录的chat_answer字段 
 4. 当在其他content页面，弹出的提示词历史中，点击了提示词，即复制了当前提示词，注入当前页面的输入框，此时可以点击存档，但是chat_uid不变，即提示词内容相同，chat_uid不变，如果输入框内容手动改变了，则生成新的chat_uid
 5. 核心思想是，页面共享提示词，相同的提示词，chat_uid相同，如果有新的chat_answer
 6. 在提示词历史页面查询时，遵守如下逻辑： 返回的提示词去重，一个提示词如果在多个平台有答案