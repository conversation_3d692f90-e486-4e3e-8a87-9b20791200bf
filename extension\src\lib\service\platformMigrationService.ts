/**
 * 平台数据迁移服务
 * 负责将现有平台的图标URL迁移为BLOB存储
 */

import { platformService } from '../dao/platformDexie'
import { Platform } from '@/types/database_entity'

export interface MigrationProgress {
  total: number
  completed: number
  failed: number
  current?: string
  errors: string[]
}

export interface MigrationResult {
  success: boolean
  totalProcessed: number
  successCount: number
  failedCount: number
  errors: string[]
  duration: number
}

export class PlatformMigrationService {
  private static instance: PlatformMigrationService
  private isRunning = false
  private progressCallback?: (progress: MigrationProgress) => void

  private constructor() {}

  static getInstance(): PlatformMigrationService {
    if (!PlatformMigrationService.instance) {
      PlatformMigrationService.instance = new PlatformMigrationService()
    }
    return PlatformMigrationService.instance
  }

  /**
   * 检查是否需要迁移
   */
  async checkMigrationNeeded(): Promise<{
    needed: boolean
    platformsToMigrate: Platform[]
    totalCount: number
  }> {
    try {
      const allPlatformsResult = await platformService.getAll()
      if (!allPlatformsResult.success) {
        throw new Error(allPlatformsResult.error)
      }

      const platforms = allPlatformsResult.data || []
      
      // 找出需要迁移的平台（没有icon_blob但有icon或url的）
      const platformsToMigrate = platforms.filter(platform => 
        !platform.icon_base64 && (platform.icon || platform.url)
      )

      return {
        needed: platformsToMigrate.length > 0,
        platformsToMigrate,
        totalCount: platforms.length
      }

    } catch (error) {
      console.error('【PlatformMigrationService】检查迁移需求失败:', error)
      return {
        needed: false,
        platformsToMigrate: [],
        totalCount: 0
      }
    }
  }

  /**
   * 执行平台图标迁移
   */
  async migratePlatformIcons(
    progressCallback?: (progress: MigrationProgress) => void
  ): Promise<MigrationResult> {
    if (this.isRunning) {
      throw new Error('Migration is already running')
    }

    this.isRunning = true
    this.progressCallback = progressCallback
    const startTime = Date.now()

    try {
      console.log('【PlatformMigrationService】开始平台图标迁移')

      // 检查需要迁移的平台
      const migrationCheck = await this.checkMigrationNeeded()
      if (!migrationCheck.needed) {
        console.log('【PlatformMigrationService】无需迁移')
        return {
          success: true,
          totalProcessed: 0,
          successCount: 0,
          failedCount: 0,
          errors: [],
          duration: Date.now() - startTime
        }
      }

      const platformsToMigrate = migrationCheck.platformsToMigrate
      const progress: MigrationProgress = {
        total: platformsToMigrate.length,
        completed: 0,
        failed: 0,
        errors: []
      }

      console.log(`【PlatformMigrationService】需要迁移${platformsToMigrate.length}个平台`)

      // 逐个处理平台
      for (const platform of platformsToMigrate) {
        try {
          progress.current = platform.name
          this.reportProgress(progress)

          console.log(`【PlatformMigrationService】检查平台: ${platform.name}`)

          // 只检查状态，不实际获取favicon（需要在对应页面执行）
          if (platform.icon_base64) {
            progress.completed++
            console.log(`【PlatformMigrationService】平台已有BLOB: ${platform.name}`)
          } else {
            progress.failed++
            const error = `${platform.name}: 需要在对应平台页面访问以获取favicon`
            progress.errors.push(error)
            console.log(`【PlatformMigrationService】平台需要更新: ${platform.name}`)
          }

          // 添加延迟避免请求过于频繁
          await this.delay(300)

        } catch (error) {
          progress.failed++
          const errorMsg = error instanceof Error ? error.message : 'Unknown error'
          const errorStr = `${platform.name}: ${errorMsg}`
          progress.errors.push(errorStr)
          console.error(`【PlatformMigrationService】处理平台异常: ${errorStr}`)
        }
      }

      // 最终进度报告
      progress.current = undefined
      this.reportProgress(progress)

      const result: MigrationResult = {
        success: progress.failed === 0,
        totalProcessed: platformsToMigrate.length,
        successCount: progress.completed,
        failedCount: progress.failed,
        errors: progress.errors,
        duration: Date.now() - startTime
      }

      console.log('【PlatformMigrationService】迁移完成:', result)
      return result

    } catch (error) {
      console.error('【PlatformMigrationService】迁移过程异常:', error)
      return {
        success: false,
        totalProcessed: 0,
        successCount: 0,
        failedCount: 0,
        errors: [error instanceof Error ? error.message : 'Unknown error'],
        duration: Date.now() - startTime
      }
    } finally {
      this.isRunning = false
      this.progressCallback = undefined
    }
  }

  /**
   * 强制重新迁移所有平台（清除现有BLOB）
   */
  async forceMigrateAllPlatforms(
    progressCallback?: (progress: MigrationProgress) => void
  ): Promise<MigrationResult> {
    if (this.isRunning) {
      throw new Error('Migration is already running')
    }

    try {
      console.log('【PlatformMigrationService】开始强制迁移所有平台')

      // 获取所有平台
      const allPlatformsResult = await platformService.getAll()
      if (!allPlatformsResult.success) {
        throw new Error(allPlatformsResult.error)
      }

      const platforms = allPlatformsResult.data || []

      // 清除所有平台的BLOB数据
      for (const platform of platforms) {
        if (platform.icon_base64) {
          await platformService.update(platform.id!, {
            icon_base64: undefined
          })
        }
      }

      // 执行迁移
      return await this.migratePlatformIcons(progressCallback)

    } catch (error) {
      console.error('【PlatformMigrationService】强制迁移失败:', error)
      return {
        success: false,
        totalProcessed: 0,
        successCount: 0,
        failedCount: 0,
        errors: [error instanceof Error ? error.message : 'Unknown error'],
        duration: 0
      }
    }
  }

  /**
   * 获取迁移状态
   */
  getMigrationStatus(): {
    isRunning: boolean
  } {
    return {
      isRunning: this.isRunning
    }
  }

  /**
   * 报告进度
   */
  private reportProgress(progress: MigrationProgress): void {
    if (this.progressCallback) {
      this.progressCallback({ ...progress })
    }
  }

  /**
   * 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * 验证迁移结果
   */
  async validateMigration(): Promise<{
    totalPlatforms: number
    withBlob: number
    withoutBlob: number
    migrationComplete: boolean
  }> {
    try {
      const allPlatformsResult = await platformService.getAll()
      if (!allPlatformsResult.success) {
        throw new Error(allPlatformsResult.error)
      }

      const platforms = allPlatformsResult.data || []
      const withBlob = platforms.filter(p => p.icon_base64).length
      const withoutBlob = platforms.length - withBlob

      return {
        totalPlatforms: platforms.length,
        withBlob,
        withoutBlob,
        migrationComplete: withoutBlob === 0
      }

    } catch (error) {
      console.error('【PlatformMigrationService】验证迁移结果失败:', error)
      return {
        totalPlatforms: 0,
        withBlob: 0,
        withoutBlob: 0,
        migrationComplete: false
      }
    }
  }
}

// 导出单例实例
export const platformMigrationService = PlatformMigrationService.getInstance()
