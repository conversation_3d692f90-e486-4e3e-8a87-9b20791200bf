/**
 * Background Script - Service Worker 主入口
 * 简洁的主入口文件，负责初始化和协调各个模块
 */

import { MessagingService } from '../lib/service/messagingService'
import { MessageHandler } from './messageHandler'
import { EventListeners } from './eventListeners'

console.log('【Background】Service Worker启动')

// 初始化事件监听器
EventListeners.initialize()

// 设置消息处理器
MessagingService.onMessage(MessageHandler.handleMessage)

console.log('【Background】Service Worker初始化完成')


