/**
 * 优雅的Favicon缓存管理服务
 * 使用Base64字符串统一存储和传输，避免Blob序列化问题
 */

import { Platform } from '@/types/database_entity'
import { MessageType } from '@/types'
import { MessagingService } from './messagingService'

export interface FaviconResult {
  success: boolean
  base64?: string
  error?: string
  source?: string
}

export class FaviconManager {
  private static instance: FaviconManager
  private cache: Map<number, boolean> = new Map() // platformId -> hasValidIcon
  private processing: Set<number> = new Set() // 正在处理的平台ID
  private readonly CACHE_DURATION = 30000 // 30秒缓存时间
  private cacheTimestamps: Map<number, number> = new Map()

  // 支持的favicon路径，按优先级排序
  private readonly FAVICON_PATHS = [
    '/favicon.svg',
    '/favicon.png', 
    '/favicon.ico',
    '/apple-touch-icon.png',
    '/favicon-32x32.png',
    '/favicon-16x16.png'
  ]

  // 支持的MIME类型
  private readonly SUPPORTED_TYPES = [
    'image/svg+xml',
    'image/png',
    'image/x-icon',
    'image/vnd.microsoft.icon',
    'image/jpeg',
    'image/webp'
  ]

  private constructor() {}

  static getInstance(): FaviconManager {
    if (!FaviconManager.instance) {
      FaviconManager.instance = new FaviconManager()
    }
    return FaviconManager.instance
  }

  /**
   * 检查并更新当前页面的favicon
   */
  async checkAndUpdateFavicon(): Promise<boolean> {
    try {
      console.log('【FaviconManager】开始检测当前平台')
      const platform = await this.detectCurrentPlatform()
      if (!platform) {
        console.log('【FaviconManager】未检测到匹配的平台')
        return false
      }

      const platformId = platform.id!
      const platformInfo = `${platform.name}(ID:${platformId})`
      console.log(`【FaviconManager-${platformInfo}】检测到平台，开始处理`)

      if (this.isCached(platformId)) {
        const cachedResult = this.cache.get(platformId) || false
        console.log(`【FaviconManager-${platformInfo}】使用缓存结果:`, cachedResult)
        return cachedResult
      }

      if (this.processing.has(platformId)) {
        console.log(`【FaviconManager-${platformInfo}】正在处理中，跳过`)
        return false
      }

      this.processing.add(platformId)

      try {
        const needsUpdate = await this.checkIfNeedsUpdate(platformId)
        console.log(`【FaviconManager-${platformInfo}】检查更新需求结果:`, needsUpdate)

        if (!needsUpdate) {
          this.updateCache(platformId, true)
          console.log(`【FaviconManager-${platformInfo}】无需更新，缓存结果`)
          return true
        }

        const result = await this.updatePlatformFavicon(platformId)
        this.updateCache(platformId, result)
        console.log(`【FaviconManager-${platformInfo}】处理完成，结果:`, result)
        return result

      } finally {
        this.processing.delete(platformId)
      }

    } catch (error) {
      console.error('【FaviconManager】检查更新favicon失败:', error)
      return false
    }
  }

  /**
   * 检测当前平台
   */
  private async detectCurrentPlatform(): Promise<Platform | null> {
    try {
      const response = await MessagingService.sendToBackground(
        MessageType.DB_PLATFORM_GET_BY_DOMAIN,
        { hostname: window.location.hostname }
      )
      return response.success ? response.data : null
    } catch (error) {
      console.error('【FaviconManager】检测平台失败:', error)
      return null
    }
  }

  /**
   * 检查平台是否需要更新favicon
   */
  private async checkIfNeedsUpdate(platformId: number): Promise<boolean> {
    try {
      console.log(`【FaviconManager-平台${platformId}】向background查询更新需求`)
      const response = await MessagingService.sendToBackground(
        MessageType.CHECK_PLATFORM_FAVICON,
        { platformId }
      )

      const needsUpdate = response.success && response.data?.needsUpdate
      console.log(`【FaviconManager-平台${platformId}】background返回结果:`, {
        success: response.success,
        needsUpdate: needsUpdate,
        reason: response.data?.reason
      })

      return needsUpdate
    } catch (error) {
      console.error(`【FaviconManager-平台${platformId}】检查更新需求失败:`, error)
      return false
    }
  }

  /**
   * 更新平台favicon
   */
  private async updatePlatformFavicon(platformId: number): Promise<boolean> {
    try {
      console.log(`【FaviconManager-平台${platformId}】开始获取favicon`)

      const faviconResult = await this.getCurrentPageFavicon()
      if (!faviconResult.success || !faviconResult.base64) {
        console.error(`【FaviconManager-平台${platformId}】获取favicon失败:`, faviconResult.error)
        return false
      }

      console.log(`【FaviconManager-平台${platformId}】favicon获取成功:`, {
        base64Length: faviconResult.base64.length,
        source: faviconResult.source
      })

      const response = await MessagingService.sendToBackground(
        MessageType.UPDATE_PLATFORM_FAVICON,
        {
          platformId,
          faviconBase64: faviconResult.base64,
          faviconUrl: faviconResult.source
        }
      )

      if (response.success) {
        console.log(`【FaviconManager-平台${platformId}】成功更新favicon缓存`)
        return true
      } else {
        console.error(`【FaviconManager-平台${platformId}】更新favicon缓存失败:`, response.error)
        return false
      }

    } catch (error) {
      console.error(`【FaviconManager-平台${platformId}】更新favicon异常:`, error)
      return false
    }
  }

  /**
   * 获取当前页面的favicon
   */
  async getCurrentPageFavicon(): Promise<FaviconResult> {
    try {
      const headFavicon = this.findFaviconFromHead()
      if (headFavicon) {
        const result = await this.fetchFavicon(headFavicon)
        if (result.success) return result
      }

      const baseUrl = `${window.location.protocol}//${window.location.host}`
      for (const path of this.FAVICON_PATHS) {
        const result = await this.fetchFavicon(baseUrl + path)
        if (result.success) return { ...result, source: baseUrl + path }
      }

      return { success: false, error: 'No valid favicon found' }

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 从HTML head中查找favicon
   */
  private findFaviconFromHead(): string | null {
    const selectors = ['link[rel="icon"]', 'link[rel="shortcut icon"]', 'link[rel="apple-touch-icon"]']

    for (const selector of selectors) {
      const link = document.querySelector(selector) as HTMLLinkElement
      if (link?.href && this.isValidFaviconUrl(link.href)) return link.href
    }
    return null
  }

  /**
   * 获取favicon数据并转换为Base64
   */
  private async fetchFavicon(url: string): Promise<FaviconResult> {
    try {
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 5000)

      const response = await fetch(url, { signal: controller.signal, cache: 'no-cache' })
      clearTimeout(timeoutId)

      if (!response.ok) return { success: false, error: `HTTP ${response.status}` }

      const blob = await response.blob()
      if (!this.validateFavicon(blob)) return { success: false, error: 'Invalid favicon data' }

      // 直接转换为Base64字符串
      const base64 = await this.blobToBase64(blob)
      return { success: true, base64, source: url }

    } catch (error) {
      return { success: false, error: error instanceof Error ? error.message : 'Fetch failed' }
    }
  }

  /**
   * 验证favicon有效性
   */
  private validateFavicon(blob: Blob): boolean {
    if (blob.size === 0 || blob.size > 1024 * 1024) return false
    return this.SUPPORTED_TYPES.some(type => blob.type.toLowerCase().includes(type.toLowerCase()))
  }

  /**
   * 验证favicon URL
   */
  private isValidFaviconUrl(url: string): boolean {
    try {
      const pathname = new URL(url).pathname.toLowerCase()
      const validExtensions = ['.ico', '.png', '.svg', '.jpg', '.jpeg', '.webp']
      return validExtensions.some(ext => pathname.endsWith(ext)) ||
             pathname.includes('favicon') || pathname.includes('icon')
    } catch {
      return false
    }
  }

  /**
   * Blob转Base64字符串（包含data:前缀）
   */
  private blobToBase64(blob: Blob): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => resolve(reader.result as string)
      reader.onerror = () => reject(new Error('Failed to convert blob to base64'))
      reader.readAsDataURL(blob)
    })
  }

  /**
   * 检查缓存
   */
  private isCached(platformId: number): boolean {
    const timestamp = this.cacheTimestamps.get(platformId)
    return timestamp ? Date.now() - timestamp < this.CACHE_DURATION : false
  }

  /**
   * 更新缓存
   */
  private updateCache(platformId: number, result: boolean): void {
    this.cache.set(platformId, result)
    this.cacheTimestamps.set(platformId, Date.now())
  }

  /**
   * 清理缓存
   */
  clearCache(): void {
    this.cache.clear()
    this.cacheTimestamps.clear()
    this.processing.clear()
  }
}

// 导出单例实例
export const faviconManager = FaviconManager.getInstance()
