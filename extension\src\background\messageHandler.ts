/**
 * Background消息处理模块
 * 负责处理来自content script和popup的消息
 */

import { chatHistoryService } from '../lib/dao/chatHistoryDexie'
import { platformService } from '../lib/dao/platformDexie'
import { StorageService } from '../lib/service/storage'
import { MessagingService } from '../lib/service/messagingService'
import { MessageType, ChromeMessage } from '../types'
import { databaseConnectionManager } from './databaseConnection'
import { healthMonitor } from './healthMonitor'

export class MessageHandler {
  /**
   * 处理消息的主要方法
   */
  static async handleMessage(
    message: ChromeMessage, 
    sender: chrome.runtime.MessageSender, 
    sendResponse: (response: any) => void
  ): Promise<void> {
    console.log('【Background】收到消息:', message.type)

    try {
      // 确保数据库连接就绪
      const isReady = await databaseConnectionManager.ensureConnection()
      if (!isReady) {
        throw new Error('Database connection not ready')
      }

      switch (message.type) {
        // 聊天历史相关操作
        case MessageType.DB_CHAT_HISTORY_CREATE:
          const createResult = await chatHistoryService.create(message.payload)
          sendResponse(createResult)
          break

        case MessageType.DB_CHAT_HISTORY_GET_LIST:
          const listResult = await chatHistoryService.getList(message.payload)
          sendResponse(listResult)
          break

        case MessageType.DB_CHAT_HISTORY_GET_UNIQUE:
          const uniqueResult = await chatHistoryService.getUniqueChats(message.payload)
          sendResponse(uniqueResult)
          break

        case MessageType.DB_CHAT_HISTORY_SEARCH:
          const searchResult = await chatHistoryService.search(message.payload.searchTerm, message.payload.params)
          sendResponse(searchResult)
          break

        case MessageType.DB_CHAT_HISTORY_UPDATE:
          const updateResult = await chatHistoryService.update(message.payload.id, message.payload.data)
          sendResponse(updateResult)
          break

        case MessageType.DB_CHAT_HISTORY_DELETE:
          const deleteResult = await chatHistoryService.delete(message.payload.id)
          sendResponse(deleteResult)
          break

        case MessageType.DB_CHAT_HISTORY_GET_BY_UID:
          const uidResult = await chatHistoryService.getByChatUid(message.payload.chatUid)
          sendResponse(uidResult)
          break

        // 平台相关操作
        case MessageType.DB_PLATFORM_GET_BY_NAME:
          const platformByNameResult = await platformService.getByName(message.payload.name)
          sendResponse(platformByNameResult)
          break

        case MessageType.DB_PLATFORM_GET_BY_DOMAIN:
          const platformByDomainResult = await platformService.findByDomain(message.payload.hostname)
          sendResponse(platformByDomainResult)
          break

        case MessageType.DB_PLATFORM_GET_LIST:
          const platformListResult = await platformService.getAll()
          sendResponse(platformListResult)
          break

        // 兼容旧接口
        case MessageType.GET_HISTORY:
          const historyResult = await chatHistoryService.getUniqueChats({ limit: 100 })
          sendResponse(historyResult.success ? 
            { success: true, data: historyResult.data } : 
            { success: false, error: historyResult.error }
          )
          break

        case MessageType.SAVE_CONVERSATION:
          await StorageService.addConversation(message.payload)
          sendResponse({ success: true })
          break

        case MessageType.UPDATE_SETTINGS:
          await StorageService.saveSettings(message.payload)
          sendResponse({ success: true })
          break

        // 提示词相关操作
        case MessageType.SYNC_PROMPT:
          await this.handleSyncPrompt(message.payload, sender)
          sendResponse({ success: true })
          break

        case MessageType.CAPTURE_PROMPT:
          await this.handleCapturePrompt(message.payload)
          sendResponse({ success: true })
          break

        // Favicon相关操作
        case MessageType.UPDATE_PLATFORM_FAVICON:
          await this.handleUpdatePlatformFavicon(message.payload)
          sendResponse({ success: true })
          break

        case MessageType.CHECK_PLATFORM_FAVICON:
          const faviconCheckResult = await MessageHandler.handleCheckPlatformFavicon(message.payload)
          sendResponse(faviconCheckResult)
          break

        default:
          MessageHandler.handleSystemMessages(message, sendResponse)
      }
    } catch (error) {
      console.error('【Background】消息处理错误:', error)
      sendResponse({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }

  /**
   * 处理系统消息
   */
  private static handleSystemMessages(message: any, sendResponse: (response: any) => void): void {
    switch (message.type) {
      case 'SW_PING':
        sendResponse({ success: true, timestamp: Date.now(), status: 'alive' })
        break
      case 'SW_WAKE_UP':
        console.log('【Background】Service Worker唤醒信号')
        sendResponse({ success: true, timestamp: Date.now(), status: 'awake' })
        break
      case 'SW_HEALTH_CHECK':
        healthMonitor.performHealthCheck().then(healthStatus => {
          sendResponse({ success: true, data: healthStatus })
        })
        break
      case 'SW_SYSTEM_STATUS':
        healthMonitor.getSystemStatus().then(systemStatus => {
          sendResponse({ success: true, data: systemStatus })
        })
        break
      default:
        sendResponse({ success: false, error: 'Unknown message type' })
    }
  }

  /**
   * 处理提示词同步
   */
  private static async handleSyncPrompt(data: any, sender: chrome.runtime.MessageSender): Promise<void> {
    try {
      const platform = await this.getPlatform(data.platform)
      if (!platform) return

      const result = await chatHistoryService.create({
        chat_prompt: data.content,
        platform_id: platform.id!,
        chat_uid: data.chatUid || Date.now().toString(),
        create_time: Date.now()
      })

      if (result.success) {
        console.log('【Background】提示词同步成功:', result.data?.id)
        
        // 通知其他标签页
        if (sender.tab?.id) {
          chrome.tabs.sendMessage(sender.tab.id, {
            type: 'PROMPT_SYNCED',
            payload: { id: result.data?.id, content: data.content }
          }).catch(() => {})
        }
      }
    } catch (error) {
      console.error('【Background】提示词同步错误:', error)
    }
  }

  /**
   * 处理提示词捕获
   */
  private static async handleCapturePrompt(data: any): Promise<void> {
    try {
      const platform = await this.getPlatform(data.platform)
      if (!platform) return

      const result = await chatHistoryService.create({
        chat_prompt: data.content,
        platform_id: platform.id!,
        chat_uid: Date.now().toString(),
        create_time: Date.now()
      })

      if (result.success) {
        console.log('【Background】提示词捕获成功:', result.data?.id)
        
        // 通知popup更新
        chrome.runtime.sendMessage({
          type: 'PROMPT_CAPTURED',
          payload: { 
            id: result.data?.id, 
            content: data.content, 
            platform: data.platform, 
            timestamp: Date.now() 
          }
        }).catch(() => {})
      }
    } catch (error) {
      console.error('【Background】提示词捕获错误:', error)
    }
  }

  /**
   * 处理平台favicon更新
   */
  private static async handleUpdatePlatformFavicon(payload: {
    platformId: number
    faviconBase64: string
    faviconUrl?: string
  }): Promise<void> {
    try {
      console.log(`【Background-平台${payload.platformId}】更新favicon:`, {
        base64Length: payload.faviconBase64.length,
        url: payload.faviconUrl
      })

      const updateData: any = { icon_base64: payload.faviconBase64 }
      if (payload.faviconUrl) {
        updateData.icon = payload.faviconUrl
      }

      const result = await platformService.update(payload.platformId, updateData)
      if (result.success) {
        console.log(`【Background-平台${payload.platformId}】favicon更新成功`)
      } else {
        console.error(`【Background-平台${payload.platformId}】favicon更新失败:`, result.error)
      }
    } catch (error) {
      console.error(`【Background-平台${payload.platformId}】favicon更新异常:`, error)
    }
  }

  /**
   * 检查平台favicon状态
   */
  private static async handleCheckPlatformFavicon(payload: {
    platformId: number
  }): Promise<{ success: boolean; data?: { needsUpdate: boolean; reason?: string }; error?: string }> {
    try {
      const platformResult = await platformService.getById(payload.platformId)
      if (!platformResult.success) {
        return { success: false, error: platformResult.error || 'Platform not found' }
      }

      const platform = platformResult.data!
      console.log(`【Background-平台${payload.platformId}】检查favicon状态:`, {
        name: platform.name,
        hasIconBase64: !!platform.icon_base64,
        base64Length: platform.icon_base64?.length || 0
      })

      // 检查icon_base64是否存在且有效
      if (!platform.icon_base64 || platform.icon_base64.length === 0) {
        return { success: true, data: { needsUpdate: true, reason: 'missing_base64' } }
      }

      // 验证Base64格式
      if (!platform.icon_base64.startsWith('data:image/')) {
        return { success: true, data: { needsUpdate: true, reason: 'invalid_base64_format' } }
      }

      return { success: true, data: { needsUpdate: false } }
    } catch (error) {
      console.error(`【Background-平台${payload.platformId}】检查favicon失败:`, error)
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
    }
  }

  /**
   * 获取或创建平台
   */
  private static async getPlatform(platformName: string) {
    if (!platformName) return null

    const name = platformName.charAt(0).toUpperCase() + platformName.slice(1).toLowerCase()
    let result = await platformService.getByName(name)

    if (result.success) return result.data

    // 尝试创建平台
    const urls: Record<string, string> = {
      'Deepseek': 'https://chat.deepseek.com',
      'Chatgpt': 'https://chat.openai.com',
      'Claude': 'https://claude.ai',
      'Gemini': 'https://gemini.google.com',
      'Kimi': 'https://kimi.moonshot.cn'
    }

    if (urls[name]) {
      const createResult = await platformService.create({ name, url: urls[name] })
      return createResult.success ? createResult.data : null
    }

    return null
  }
}
