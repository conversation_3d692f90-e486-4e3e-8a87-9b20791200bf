# 数据结构重构需求

现在的表结构设计是
`chat_history`表，包含如下字段：
  * id: 主键，自增
  * chat_prompt: 聊天页面输入框的提示词
  * chat_answer: 聊天页面输入提示词对应的回答
  * chat_uid: 聊天的唯一id，暂时使用秒级别时间戳（提供索引）
  * platform_id: 平台，比如是deepseek还是chatgpt等等
  * tags: 标签，数组类型 暂时为空
  * chat_group_name：聊天组名称，一般位于对话的顶部
  * chat_sort: 有的平台，比如像deeepseek可以获得本次问题在本对话的序号
  * p_uid: 从当前平台获得的，本次文词在当前聊天平台的唯一id，有的是session_id+序号
  * create_time 创建时间 取时间戳
  * is_synced: 是否已同步过，0否1是，默认0
  * is_delete: 是否删除，0否1是，默认0


  但是这样设计存在一个缺陷，我在查询历史提示词列表时，以及每个提示词对应有哪些平台记录时，会特别不方便。因此我想重构数据库的设计，新增一个表`chat_prompt`，包含如下字段：
  * id: 主键，自增
  * chat_prompt: 聊天页面输入框的提示词 （唯一键，不能重复）
  * chat_uid: 聊天的唯一id，暂时使用秒级别时间戳（提供索引）
  * create_time 创建时间 取时间戳   
  * is_synced: 是否已同步过，0否1是，默认0
  * is_delete: 是否删除，0否1是，默认0

  存入提示词时，存入两个表，`chat_prompt`表和`chat_history`表，`chat_prompt`表的`chat_uid`和`chat_history`表的`chat_uid`保持一致，这样就实现了`chat_prompt`表和`chat_history`表的一对多关系。当获得答案时，只存入对应平台的`chat_history`表即可，如果tiuic


  在历史提示词页面显示时，查询`chat_prompt`表，获得去重后的提示词列表，然后根据`chat_uid`查询对应的`chat_history`表，获得每个提示词对应的所有平台的回答。