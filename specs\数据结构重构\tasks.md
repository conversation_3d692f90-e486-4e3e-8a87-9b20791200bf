# 实施计划

## 任务概述

本次数据结构重构将分为6个主要阶段，每个阶段包含若干具体任务。按照依赖关系和风险控制原则，采用渐进式实施策略。

## 阶段1：数据模型和类型定义

- [ ] 1.1 定义ChatPrompt实体类型
  - 在 `types/database_entity.ts` 中添加 ChatPrompt 接口定义
  - 包含所有必需字段：id, chat_prompt, chat_uid, create_time, is_synced, is_delete
  - _需求: 需求1_

- [ ] 1.2 更新ChatHistory实体类型
  - 移除 ChatHistory 接口中的 chat_prompt 字段
  - 确保其他字段定义正确，特别是 is_answered 字段
  - _需求: 需求2_

- [ ] 1.3 定义ChatPrompt相关DTO类型
  - 在 `types/database_dto.ts` 中添加 CreateChatPromptInput, UpdateChatPromptInput
  - 添加 ChatPromptQueryParams 查询参数类型
  - 添加 ChatPromptWithPlatforms 联合查询结果类型
  - _需求: 需求4_

- [ ] 1.4 更新现有DTO类型
  - 修改 CreateChatHistoryInput，移除 chat_prompt 字段（保持向后兼容）
  - 添加新的查询结果类型，支持提示词和历史记录的联合查询
  - _需求: 需求6_

## 阶段2：数据库结构升级

- [ ] 2.1 升级Dexie数据库版本
  - 在 `lib/database/dexie.ts` 中添加版本5的数据库结构定义
  - 添加 chatPrompt 表定义和索引
  - 更新 chatHistory 表结构，移除 chat_prompt 索引
  - _需求: 需求1, 需求2_

- [ ] 2.2 实现数据迁移逻辑
  - 编写数据迁移函数，从现有 chat_history 提取唯一提示词
  - 创建对应的 chat_prompt 记录，保持 chat_uid 一致性
  - 实现迁移过程的错误处理和回滚机制
  - _需求: 需求5_

- [ ] 2.3 添加数据验证逻辑
  - 实现迁移后的数据完整性验证
  - 验证 chat_uid 关联关系正确性
  - 添加迁移日志和状态跟踪
  - _需求: 需求5_

## 阶段3：数据访问层实现

- [ ] 3.1 创建ChatPromptService
  - 在 `lib/dao/` 目录下创建 `chatPromptDexie.ts`
  - 实现基础CRUD操作：create, getById, update, delete
  - 实现 findByPrompt 方法，支持提示词唯一性查询
  - 实现 getList 方法，支持分页和排序
  - _需求: 需求1, 需求4_

- [ ] 3.2 重构ChatHistoryService
  - 修改 create 方法，实现双表操作逻辑
  - 添加 createWithPrompt 方法，处理提示词复用逻辑
  - 实现 getFullChatInfo 方法，根据 chat_uid 获取完整信息
  - 保持现有方法签名不变，确保向后兼容
  - _需求: 需求3, 需求6_

- [ ] 3.3 扩展数据库查询方法
  - 在 EchoSyncDatabase 类中添加 getPromptsWithPlatforms 方法
  - 实现 getChatPromptDetail 方法，获取提示词详情
  - 优化现有查询方法，适配新的表结构
  - _需求: 需求4_

## 阶段4：业务逻辑层适配

- [ ] 4.1 更新数据库代理服务
  - 修改 `lib/service/databaseProxy.ts` 中的 ChatHistoryDatabaseProxy
  - 添加 ChatPromptDatabaseProxy 类
  - 实现消息传递机制，支持新的数据操作
  - _需求: 需求6_

- [ ] 4.2 实现数据存储业务逻辑
  - 创建统一的聊天记录创建服务
  - 实现提示词复用逻辑：检查已存在提示词，复用 chat_uid
  - 实现答案更新逻辑：仅更新 chat_history 表
  - 添加事务处理，确保双表操作的原子性
  - _需求: 需求3_

- [ ] 4.3 实现查询业务逻辑
  - 创建提示词列表查询服务，支持平台统计
  - 实现提示词详情查询，包含所有平台的历史记录
  - 优化查询性能，使用批量查询和缓存策略
  - _需求: 需求4_

## 阶段5：API接口层更新

- [ ] 5.1 扩展现有API接口
  - 保持现有 ChatHistoryService 公共接口不变
  - 内部实现适配新的数据结构
  - 添加新的查询方法，支持提示词维度的数据获取
  - _需求: 需求6_

- [ ] 5.2 添加新的API接口
  - 添加 getPromptsWithPlatforms 接口
  - 添加 getPromptDetail 接口
  - 实现搜索功能的适配，支持跨表搜索
  - _需求: 需求4_

- [ ] 5.3 更新消息处理机制
  - 在 background script 中添加新的消息处理器
  - 更新消息类型定义，支持新的数据操作
  - 确保前端调用的兼容性
  - _需求: 需求6_

## 阶段6：测试和验证

- [ ] 6.1 编写单元测试
  - 为 ChatPromptService 编写完整的单元测试
  - 更新 ChatHistoryService 的单元测试
  - 测试数据迁移逻辑的正确性
  - _需求: 需求1-6_

- [ ] 6.2 集成测试
  - 测试双表操作的事务一致性
  - 测试查询功能的正确性和性能
  - 测试API接口的向后兼容性
  - _需求: 需求3, 需求4, 需求6_

- [ ] 6.3 数据迁移测试
  - 准备测试数据，模拟各种场景
  - 测试迁移过程的稳定性和数据完整性
  - 测试回滚机制的有效性
  - 性能测试，确保迁移后查询效率提升
  - _需求: 需求5_

## 实施顺序和依赖关系

```
阶段1 → 阶段2 → 阶段3 → 阶段4 → 阶段5 → 阶段6
  ↓       ↓       ↓       ↓       ↓       ↓
 1.1    2.1     3.1     4.1     5.1     6.1
 1.2 → 2.2 → 3.2 → 4.2 → 5.2 → 6.2
 1.3    2.3     3.3     4.3     5.3     6.3
 1.4
```

## 风险控制措施

- **数据备份**: 每个阶段开始前备份数据库
- **渐进发布**: 分阶段测试和发布，确保每个阶段稳定后再进行下一阶段
- **回滚准备**: 每个阶段都准备回滚方案
- **监控告警**: 实施过程中监控数据库性能和错误率
