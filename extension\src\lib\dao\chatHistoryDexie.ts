import { dexieDatabase, ChatHistoryWithPlatform } from '../database/dexie'
import { ChatHistory } from '../../types/database_entity'
import {
  CreateChatHistoryInput,
  UpdateChatHistoryInput,
  ChatHistoryQueryParams,
  PaginatedResult,
  SearchResult,
  DatabaseResult
} from '../../types/database_dto'

export class ChatHistoryService {
  private static instance: ChatHistoryService

  public static getInstance(): ChatHistoryService {
    if (!ChatHistoryService.instance) {
      ChatHistoryService.instance = new ChatHistoryService()
    }
    return ChatHistoryService.instance
  }

  /**
   * 创建聊天历史记录
   */
  async create(input: CreateChatHistoryInput): Promise<DatabaseResult<ChatHistory>> {
    try {
      console.log('【EchoSync】Starting create chat history with input:', input)
      await dexieDatabase.initialize()
      console.log('【EchoSync】Database initialized successfully')

      // 检查是否存在相同提示词，如果存在则复用chat_uid
      const existingUid = await this.findExistingChatUid(input.chat_prompt)
      console.log('【EchoSync】Existing UID check result:', existingUid)

      const now = Date.now()
      const data: Omit<ChatHistory, 'id'> = {
        chat_prompt: input.chat_prompt,
        chat_answer: input.chat_answer || null,
        chat_uid: existingUid || input.chat_uid || Date.now().toString(),
        platform_id: input.platform_id,
        tags: typeof input.tags === 'string' ? input.tags : JSON.stringify(input.tags || []),
        chat_group_name: input.chat_group_name || null,
        chat_sort: input.chat_sort || 0,
        p_uid: input.p_uid || null,
        is_synced: 0,
        is_delete: 0,
        create_time: input.create_time || now
      }

      console.log('【EchoSync】Prepared data for insertion:', data)
      const id = await dexieDatabase.chatHistory.add(data as ChatHistory)
      console.log('【EchoSync】Data inserted with ID:', id)

      const record = await dexieDatabase.chatHistory.get(id)
      console.log('【EchoSync】Retrieved record:', record)

      return {
        success: true,
        data: record!
      }
    } catch (error) {
      console.error('【EchoSync】Create chat history error:', error)
      console.error('【EchoSync】Error details:', {
        name: error instanceof Error ? error.name : 'Unknown',
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace'
      })
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 根据ID获取聊天历史记录
   */
  async getById(id: number): Promise<DatabaseResult<ChatHistory>> {
    try {
      await dexieDatabase.initialize()
      
      const record = await dexieDatabase.chatHistory
        .where('id')
        .equals(id)
        .and(item => item.is_delete === 0)
        .first()

      if (!record) {
        return {
          success: false,
          error: 'Record not found'
        }
      }

      return {
        success: true,
        data: record
      }
    } catch (error) {
      console.error('Get chat history by id error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 更新聊天历史记录
   */
  async update(id: number, input: UpdateChatHistoryInput): Promise<DatabaseResult<ChatHistory>> {
    try {
      await dexieDatabase.initialize()
      
      const updateData: Partial<ChatHistory> = {
        ...input,
        tags: input.tags ? JSON.stringify(input.tags) : undefined
      }

      await dexieDatabase.chatHistory.update(id, updateData)
      const record = await dexieDatabase.chatHistory.get(id)

      if (!record) {
        return {
          success: false,
          error: 'Record not found after update'
        }
      }

      return {
        success: true,
        data: record
      }
    } catch (error) {
      console.error('Update chat history error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 软删除聊天历史记录
   */
  async delete(id: number): Promise<DatabaseResult<boolean>> {
    try {
      await dexieDatabase.initialize()
      
      await dexieDatabase.chatHistory.update(id, { is_delete: 1 })

      return {
        success: true,
        data: true
      }
    } catch (error) {
      console.error('Delete chat history error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 批量删除聊天历史记录
   */
  async batchDelete(ids: number[]): Promise<DatabaseResult<boolean>> {
    try {
      await dexieDatabase.initialize()
      
      await dexieDatabase.chatHistory
        .where('id')
        .anyOf(ids)
        .modify({ is_delete: 1 })

      return {
        success: true,
        data: true
      }
    } catch (error) {
      console.error('Batch delete chat history error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 获取聊天历史列表（带分页）
   */
  async getList(params: ChatHistoryQueryParams = {}): Promise<DatabaseResult<PaginatedResult<ChatHistoryWithPlatform>>> {
    try {
      await dexieDatabase.initialize()
      
      const {
        page = 1,
        limit = 20,
        platform_id,
        order_by = 'create_time',
        order_direction = 'DESC'
      } = params

      const offset = (page - 1) * limit

      const result = await dexieDatabase.getChatHistoryWithPlatform({
        limit,
        offset,
        platform_id,
        order_by,
        order_direction
      })

      // 获取总数
      let totalQuery = dexieDatabase.chatHistory.where('is_delete').equals(0)
      if (platform_id) {
        totalQuery = totalQuery.and(item => item.platform_id === platform_id)
      }
      const total = await totalQuery.count()

      return {
        success: true,
        data: {
          data: result,
          total,
          page,
          limit,
          totalPages: Math.ceil(total / limit)
        }
      }
    } catch (error) {
      console.error('Get chat history list error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 搜索聊天历史
   */
  async search(searchTerm: string, params: ChatHistoryQueryParams = {}): Promise<DatabaseResult<SearchResult<ChatHistoryWithPlatform>>> {
    try {
      await dexieDatabase.initialize()
      
      const {
        page = 1,
        limit = 20,
        platform_id
      } = params

      const offset = (page - 1) * limit

      const results = await dexieDatabase.searchChatHistory(searchTerm, {
        limit: limit + offset, // 获取更多数据用于分页
        platform_id
      })

      // 手动分页
      const paginatedResults = results.slice(offset, offset + limit)

      return {
        success: true,
        data: {
          data: paginatedResults,
          total: results.length,
          page,
          limit,
          totalPages: Math.ceil(results.length / limit),
          searchTerm
        }
      }
    } catch (error) {
      console.error('Search chat history error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 获取去重的聊天历史
   */
  async getUniqueChats(params: { limit?: number; order_direction?: 'ASC' | 'DESC' } = {}): Promise<DatabaseResult<ChatHistoryWithPlatform[]>> {
    try {
      await dexieDatabase.initialize()
      
      const result = await dexieDatabase.getUniqueChats(params)

      return {
        success: true,
        data: result
      }
    } catch (error) {
      console.error('Get unique chats error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 根据提示词内容查找聊天历史
   */
  async findByChatPrompt(prompt: string): Promise<DatabaseResult<ChatHistory | null>> {
    try {
      await dexieDatabase.initialize()

      const result = await dexieDatabase.chatHistory
        .where('chat_prompt')
        .equals(prompt)
        .and(item => item.is_delete === 0)
        .first()

      return {
        success: true,
        data: result || null
      }
    } catch (error) {
      console.error('Find chat history by prompt error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 根据提示词内容查找已存在的chat_uid
   */
  async findExistingChatUid(prompt: string): Promise<string | null> {
    const result = await this.findByChatPrompt(prompt)
    return result.success && result.data ? result.data.chat_uid : null
  }

  /**
   * 根据 chat_uid 获取聊天历史
   */
  async getByChatUid(chatUid: string): Promise<DatabaseResult<ChatHistory[]>> {
    try {
      await dexieDatabase.initialize()

      const records = await dexieDatabase.chatHistory
        .where('chat_uid')
        .equals(chatUid)
        .and(item => item.is_delete === 0)
        .toArray()

      return {
        success: true,
        data: records
      }
    } catch (error) {
      console.error('Get chat history by chat_uid error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }
}

// 导出单例实例
export const chatHistoryService = ChatHistoryService.getInstance()
