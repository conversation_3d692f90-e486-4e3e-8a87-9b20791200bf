# 需求文档

## 介绍

当前系统使用单一的 `chat_history` 表存储聊天提示词和回答，在查询历史提示词列表以及每个提示词对应的平台记录时存在不便。需要重构数据库设计，将提示词和历史记录分离，实现更高效的数据查询和管理。

## 需求

### 需求 1 - 新增 chat_prompt 表

**用户故事：** 作为系统管理员，我需要一个独立的提示词表来存储去重的聊天提示词，以便更高效地管理和查询历史提示词。

#### 验收标准

1. When 系统初始化时，系统应当创建 `chat_prompt` 表，包含字段：id（主键，自增）、chat_prompt（提示词内容，唯一键）、chat_uid（聊天唯一ID）、create_time（创建时间）、is_synced（同步状态）、is_delete（删除状态）。
2. When 提示词内容重复时，系统应当确保 `chat_prompt` 表中不存在重复的提示词记录。
3. When 查询提示词时，系统应当能够通过 chat_prompt 字段进行唯一性查询。

### 需求 2 - 重构 chat_history 表结构

**用户故事：** 作为开发者，我需要调整 `chat_history` 表的字段，移除冗余的提示词存储，建立与 `chat_prompt` 表的关联关系。

#### 验收标准

1. When 重构完成后，`chat_history` 表应当保留字段：id、chat_answer、chat_uid、platform_id、tags、chat_group_name、chat_sort、p_uid、create_time、is_synced、is_answered、is_delete。
2. When 重构完成后，`chat_history` 表应当移除 chat_prompt 字段。
3. When 存储聊天记录时，系统应当通过 chat_uid 字段与 `chat_prompt` 表建立关联关系。

### 需求 3 - 实现数据存储逻辑

**用户故事：** 作为用户，当我输入提示词时，系统需要正确地将数据存储到两个表中，确保数据一致性。

#### 验收标准

1. When 用户输入新提示词时，系统应当同时在 `chat_prompt` 表和 `chat_history` 表中创建记录，两表的 chat_uid 保持一致。
2. When 用户输入已存在的提示词时，系统应当复用 `chat_prompt` 表中的 chat_uid，在 `chat_history` 表中创建新记录。
3. When 获得答案时，系统应当仅更新 `chat_history` 表中对应记录的 chat_answer 和 is_answered 字段。

### 需求 4 - 实现查询功能

**用户故事：** 作为用户，我需要能够查询历史提示词列表，并看到每个提示词在不同平台的使用情况。

#### 验收标准

1. When 查询历史提示词列表时，系统应当从 `chat_prompt` 表获取去重的提示词列表。
2. When 查看提示词详情时，系统应当根据 chat_uid 查询 `chat_history` 表，获取该提示词在所有平台的记录。
3. When 显示提示词列表时，系统应当显示每个提示词对应的平台logo，如果同一提示词在多个平台使用过，则显示多个平台logo。

### 需求 5 - 数据迁移

**用户故事：** 作为系统管理员，我需要将现有的 `chat_history` 表数据迁移到新的表结构中，确保数据不丢失。

#### 验收标准

1. When 执行数据迁移时，系统应当从现有 `chat_history` 表中提取所有唯一的 chat_prompt，创建对应的 `chat_prompt` 记录。
2. When 数据迁移完成后，现有 `chat_history` 表中的记录应当保持完整，仅移除 chat_prompt 字段。
3. When 迁移过程中出现错误时，系统应当回滚所有更改，确保数据完整性。

### 需求 6 - API 接口适配

**用户故事：** 作为前端开发者，我需要现有的API接口能够适配新的数据结构，保持向后兼容性。

#### 验收标准

1. When 调用创建聊天记录接口时，系统应当自动处理两表的数据存储逻辑。
2. When 调用查询接口时，系统应当返回包含提示词信息的完整数据结构。
3. When 使用现有接口时，系统应当保持API签名不变，确保前端代码无需大幅修改。
