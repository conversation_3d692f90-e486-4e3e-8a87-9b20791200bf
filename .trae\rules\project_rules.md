# EchoAIExtention 项目开发规则

## 1. 核心开发原则

### 1.1 代码质量要求
- 单个ts文件不超过300行，超过需拆分
- 继承不超过2级，优先使用组合模式
- 子类优先扩充父类，避免完全覆盖
- 所有数据库操作必须通过background脚本处理

### 1.2 TypeScript 规范
- 使用严格模式 (`"strict": true`)
- 禁用 `any` 类型，保持类型定义明确
- 函数必须声明返回类型
- 使用 `interface` 定义对象类型
- 类型定义统一放在 `types/` 目录

### 1.3 React 开发规范
- 使用函数组件和 Hooks
- 组件采用 PascalCase 命名
- Hook 必须以 `use` 开头
- 使用 `.tsx` 扩展名
- 性能关键组件使用 `React.memo`

## 2. 项目架构规范

### 2.1 Monorepo 结构
- npm workspaces 管理子项目
- 共享依赖置于根目录
- 子项目间使用 npm link 引用

### 2.2 命名规范
- 目录：kebab-case
- 组件目录：PascalCase
- 工具函数：camelCase
- 配置文件：UPPER_CASE
- 组件文件：PascalCase.tsx
- 工具文件：camelCase.ts
- 类型文件：camelCase.types.ts

## 3. Chrome 插件规范

### 3.1 基础规范
- 遵循 Manifest V3 标准
- 使用 Service Worker
- 最小化权限申请
- 定期更新 CSP

### 3.2 通信与存储
- 使用 `chrome.runtime.sendMessage` 通信
- 消息类型定义在 `types/messages.ts`
- 检查 `chrome.runtime.lastError`
- 异步操作使用 Promise
- 优先使用 `chrome.storage.sync`
- 大数据用 `chrome.storage.local`
- 键名前缀：`echo_`

## 4. Next.js 网站规范

### 4.1 基础架构
- 使用 Next.js 14+ App Router
- 页面组件在 `app/` 目录
- API 路由在 `app/api/`
- 标记客户端组件 `'use client'`

### 4.2 数据处理
- 使用 `fetch` 请求数据
- 实现缓存和重验证
- 处理错误边界
- 使用 Suspense 加载
- Supabase 操作使用 RLS
- 敏感操作在服务端

## 5. 状态管理规范

### 5.1 Zustand 使用
- Store 文件在 `stores/`
- 使用 TypeScript 类型
- 使用 persist 持久化
- 避免存储大量数据
- 使用 Immer 更新
- 复杂更新用 reducer

**注意**：特殊情况需在代码审查时说明并获得同意。