/**
 * FaviconManager测试脚本
 * 用于验证新的favicon处理逻辑
 */

import { faviconManager } from '@/lib/service/faviconManager'

/**
 * 测试FaviconManager的基本功能
 */
export class FaviconManagerTest {
  
  /**
   * 运行所有测试
   */
  static async runAllTests(): Promise<void> {
    console.group('【FaviconManagerTest】开始运行测试')
    
    try {
      await this.testBasicFunctionality()
      await this.testFaviconExtraction()
      await this.testCacheSystem()
      await this.testErrorHandling()
      
      console.log('【FaviconManagerTest】✅ 所有测试通过')
    } catch (error) {
      console.error('【FaviconManagerTest】❌ 测试失败:', error)
    }
    
    console.groupEnd()
  }

  /**
   * 测试基本功能
   */
  static async testBasicFunctionality(): Promise<void> {
    console.log('【FaviconManagerTest】测试基本功能...')
    
    try {
      // 测试单例模式
      const instance1 = faviconManager
      const instance2 = faviconManager
      
      if (instance1 !== instance2) {
        throw new Error('单例模式测试失败')
      }
      
      console.log('【FaviconManagerTest】✅ 单例模式测试通过')
      
      // 测试缓存清理
      faviconManager.clearCache()
      console.log('【FaviconManagerTest】✅ 缓存清理测试通过')
      
    } catch (error) {
      console.error('【FaviconManagerTest】❌ 基本功能测试失败:', error)
      throw error
    }
  }

  /**
   * 测试favicon提取功能
   */
  static async testFaviconExtraction(): Promise<void> {
    console.log('【FaviconManagerTest】测试favicon提取功能...')
    
    try {
      // 测试获取当前页面favicon
      const result = await faviconManager.getCurrentPageFavicon()
      
      console.log('【FaviconManagerTest】Favicon提取结果:', {
        success: result.success,
        hasBlob: !!result.blob,
        hasDataUrl: !!result.dataUrl,
        source: result.source,
        error: result.error
      })
      
      if (result.success) {
        console.log('【FaviconManagerTest】✅ Favicon提取成功')
        
        // 验证blob数据
        if (result.blob) {
          console.log('【FaviconManagerTest】Blob信息:', {
            size: result.blob.size,
            type: result.blob.type
          })
          
          if (result.blob.size > 0 && result.blob.type.startsWith('image/')) {
            console.log('【FaviconManagerTest】✅ Blob数据验证通过')
          } else {
            console.warn('【FaviconManagerTest】⚠️ Blob数据可能有问题')
          }
        }
      } else {
        console.log('【FaviconManagerTest】ℹ️ 当前页面未找到favicon:', result.error)
      }
      
    } catch (error) {
      console.error('【FaviconManagerTest】❌ Favicon提取测试失败:', error)
      throw error
    }
  }

  /**
   * 测试缓存系统
   */
  static async testCacheSystem(): Promise<void> {
    console.log('【FaviconManagerTest】测试缓存系统...')
    
    try {
      // 清理缓存
      faviconManager.clearCache()
      
      // 第一次调用（应该执行完整流程）
      const startTime1 = Date.now()
      const result1 = await faviconManager.checkAndUpdateFavicon()
      const duration1 = Date.now() - startTime1
      
      console.log('【FaviconManagerTest】第一次调用结果:', {
        success: result1,
        duration: duration1 + 'ms'
      })
      
      // 第二次调用（应该使用缓存）
      const startTime2 = Date.now()
      const result2 = await faviconManager.checkAndUpdateFavicon()
      const duration2 = Date.now() - startTime2
      
      console.log('【FaviconManagerTest】第二次调用结果:', {
        success: result2,
        duration: duration2 + 'ms'
      })
      
      // 缓存应该使第二次调用更快
      if (duration2 < duration1) {
        console.log('【FaviconManagerTest】✅ 缓存系统工作正常')
      } else {
        console.log('【FaviconManagerTest】ℹ️ 缓存效果不明显，可能是网络或其他因素')
      }
      
    } catch (error) {
      console.error('【FaviconManagerTest】❌ 缓存系统测试失败:', error)
      throw error
    }
  }

  /**
   * 测试错误处理
   */
  static async testErrorHandling(): Promise<void> {
    console.log('【FaviconManagerTest】测试错误处理...')
    
    try {
      // 测试无效URL的处理
      const faviconResult = await faviconManager.getCurrentPageFavicon()
      
      // 不管成功还是失败，都应该有合理的响应
      if (typeof faviconResult.success !== 'boolean') {
        throw new Error('返回结果格式不正确')
      }
      
      if (!faviconResult.success && !faviconResult.error) {
        throw new Error('失败时应该提供错误信息')
      }
      
      console.log('【FaviconManagerTest】✅ 错误处理测试通过')
      
    } catch (error) {
      console.error('【FaviconManagerTest】❌ 错误处理测试失败:', error)
      throw error
    }
  }

  /**
   * 性能测试
   */
  static async performanceTest(): Promise<void> {
    console.log('【FaviconManagerTest】开始性能测试...')
    
    const iterations = 5
    const durations: number[] = []
    
    for (let i = 0; i < iterations; i++) {
      faviconManager.clearCache() // 清理缓存确保每次都是完整流程
      
      const startTime = Date.now()
      await faviconManager.checkAndUpdateFavicon()
      const duration = Date.now() - startTime
      
      durations.push(duration)
      console.log(`【FaviconManagerTest】第${i + 1}次测试: ${duration}ms`)
    }
    
    const avgDuration = durations.reduce((a, b) => a + b, 0) / durations.length
    const maxDuration = Math.max(...durations)
    const minDuration = Math.min(...durations)
    
    console.log('【FaviconManagerTest】性能测试结果:', {
      平均时间: avgDuration.toFixed(2) + 'ms',
      最大时间: maxDuration + 'ms',
      最小时间: minDuration + 'ms',
      测试次数: iterations
    })
    
    if (avgDuration < 2000) {
      console.log('【FaviconManagerTest】✅ 性能测试通过（平均响应时间 < 2秒）')
    } else {
      console.warn('【FaviconManagerTest】⚠️ 性能可能需要优化（平均响应时间 > 2秒）')
    }
  }

  /**
   * 兼容性测试
   */
  static async compatibilityTest(): Promise<void> {
    console.log('【FaviconManagerTest】开始兼容性测试...')
    
    try {
      // 测试必要的API是否可用
      const requiredAPIs = [
        'fetch',
        'URL',
        'FileReader',
        'Blob',
        'document.querySelector',
        'chrome.runtime'
      ]
      
      for (const api of requiredAPIs) {
        const parts = api.split('.')
        let obj: any = window
        
        for (const part of parts) {
          if (obj && typeof obj[part] !== 'undefined') {
            obj = obj[part]
          } else {
            throw new Error(`缺少必要的API: ${api}`)
          }
        }
      }
      
      console.log('【FaviconManagerTest】✅ 兼容性测试通过')
      
    } catch (error) {
      console.error('【FaviconManagerTest】❌ 兼容性测试失败:', error)
      throw error
    }
  }
}

// 如果在浏览器环境中，自动运行测试
if (typeof window !== 'undefined') {
  // 延迟执行，确保页面加载完成
  setTimeout(() => {
    FaviconManagerTest.runAllTests()
  }, 1000)
}
