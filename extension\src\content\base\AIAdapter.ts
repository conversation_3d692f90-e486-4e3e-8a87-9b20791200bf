import { AIPlatform, Conversation } from '@/types'
import { Platform } from '@/types/database_entity'
import { platformDatabaseProxy } from '@/lib/service/databaseProxy'
import { DOMUtils } from './DOMUtils'
import { FloatingBubble } from './FloatingBubble'
import { ArchiveButton } from './ArchiveButton'
import { DragHandler } from './DragHandler'
import { InputManager } from './InputManager'
import { HistoryManager } from './HistoryManager'
import { faviconManager } from '@/lib/service/faviconManager'

/**
 * AI适配器基类
 * 重构后的主适配器类，集成所有拆分的模块
 */
export abstract class AIAdapter {
  // 基础属性
  protected platform: { name: string; id: AIPlatform; url: string }
  protected selectors: { inputField: string; sendButton: string; messageContainer: string }
  protected currentPlatform: Platform | null = null

  // 模块实例
  protected floatingBubble: FloatingBubble
  protected archiveButton: ArchiveButton
  protected dragHandler: DragHandler | null = null
  protected inputManager: InputManager
  protected historyManager: HistoryManager

  constructor(platform: { name: string; id: AIPlatform; url: string }, selectors: any) {
    this.platform = platform
    this.selectors = selectors

    // 初始化模块
    this.floatingBubble = new FloatingBubble()
    this.archiveButton = new ArchiveButton()
    this.inputManager = new InputManager()
    this.historyManager = new HistoryManager()

    // 设置事件监听
    this.setupEventListeners()
  }

  /**
   * 抽象方法 - 子类必须实现
   */
  abstract extractConversation(): Promise<Conversation | null>
  abstract isValidPage(): boolean

  /**
   * 注入提示词 - 提供默认实现，子类可以重写
   */
  async injectPrompt(prompt: string): Promise<void> {
    // 使用输入管理器注入提示词
    this.inputManager.injectPrompt(prompt)

    // 等待一下确保内容已更新
    await new Promise(resolve => setTimeout(resolve, 100))
  }

  /**
   * 初始化通用功能
   */
  async initUniversalFeatures(): Promise<void> {
    console.log('【EchoSync】Initializing universal features for', this.platform.name)

    try {
            // 创建悬浮气泡
      console.log('【EchoSync】Creating floating bubble...')
      const bubble = this.floatingBubble.createFloatingBubble()
      console.log('【EchoSync】Floating bubble created:', bubble)
            
      // 等待页面加载
      console.log('【EchoSync】Waiting for page load...')
      await DOMUtils.waitForPageLoad()
      console.log('【EchoSync】Page loaded')

      // 加载当前平台信息
      console.log('【EchoSync】Loading current platform...')
      await this.loadCurrentPlatform()
      console.log('【EchoSync】Current platform loaded:', this.currentPlatform)

      // 查找并设置输入元素
      console.log('【EchoSync】Finding and setting up input element...')
      await this.inputManager.findAndSetupInputElement(this.selectors)
      console.log('【EchoSync】Input element setup complete')



      if (bubble) {
        // 初始化拖拽处理
        console.log('【EchoSync】Initializing drag handler...')
        this.dragHandler = new DragHandler(bubble)
        console.log('【EchoSync】Drag handler initialized')

        // 将拖拽处理器暴露给气泡元素，以便点击事件检查
        ;(bubble as any).dragHandler = this.dragHandler
      }

      // 添加存档按钮
      console.log('【EchoSync】Adding archive button...')
      await this.archiveButton.addArchiveButton(this.selectors)
      console.log('【EchoSync】Archive button added')

      // 设置输入监听
      console.log('【EchoSync】Setting up input listeners...')
      this.inputManager.setupInputFocusListener(this.selectors)
      this.inputManager.setupSendListener(this.selectors)
      console.log('【EchoSync】Input listeners setup complete')

      console.log('【EchoSync】Universal features initialized successfully')
    } catch (error) {
      console.error('【EchoSync】Error initializing universal features:', error)
      console.error('【EchoSync】Error stack:', error.stack)
    }
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 输入聚焦事件
    document.addEventListener('echosync:input-focused', (event: any) => {
      const { inputElement } = event.detail
      this.floatingBubble.moveToInputField(inputElement)
    })

    // 输入变化事件
    document.addEventListener('echosync:input-changed', (event: any) => {
      const { value } = event.detail
      this.archiveButton.updateArchiveButtonState(value)
    })

    // 自动存档事件
    document.addEventListener('echosync:auto-archive', (event: any) => {
      const { prompt } = event.detail
      this.archiveButton.autoArchivePrompt(prompt, this.currentPlatform)
    })

    // 存档当前提示词事件
    document.addEventListener('echosync:archive-current-prompt', () => {
      this.archiveButton.archiveCurrentPrompt(
        () => this.inputManager.getCurrentInput(),
        this.currentPlatform,
        (message, type) => this.showNotification(message, type)
      )
    })

    // 注入提示词事件
    document.addEventListener('echosync:inject-prompt', (event: any) => {
      const { prompt } = event.detail
      this.inputManager.injectPrompt(prompt)
    })

    // 边界回弹事件
    document.addEventListener('echosync:snap-to-boundary', () => {
      this.floatingBubble.snapToBoundary()
    })

    // 处理历史点击事件
    document.addEventListener('echosync:handle-history-click', (event: any) => {
      const { chat } = event.detail
      this.handleHistoryItemClick(chat)
    })

    // 处理发送前捕捉事件
    document.addEventListener('echosync:prompt-send', (event: any) => {
      const { prompt } = event.detail
      this.handlePromptSend(prompt)
    })

    // 请求历史数据事件
    document.addEventListener('echosync:request-history-data', () => {
      this.historyManager.showHistoryBubble()
    })

    // 显示存储的提示词事件
    document.addEventListener('echosync:show-stored-prompts', () => {
      this.historyManager.showHistoryBubble()
    })

    // 调试功能事件
    document.addEventListener('echosync:debug-features', () => {
      this.debugFeatures()
    })
  }

  /**
   * 加载当前平台信息
   */
  protected async loadCurrentPlatform(): Promise<void> {
    try {
      console.log('【EchoSync】Loading current platform...')
      console.log('【EchoSync】Adapter platform info:', this.platform)
      console.log('【EchoSync】Current URL:', window.location.href)

      const platformsResult = await platformDatabaseProxy.getAll()
      if (platformsResult.success) {
        console.log('【EchoSync】Available platforms in database:', platformsResult.data)

        this.currentPlatform = platformsResult.data.find((p: Platform) => {
          const nameMatch = p.name === this.platform.name
          const urlMatch = window.location.href.includes(p.url.replace('https://', '').replace('http://', ''))

          console.log(`【EchoSync】Checking platform ${p.name}:`)
          console.log(`  - Name match (${p.name} === ${this.platform.name}): ${nameMatch}`)
          console.log(`  - URL match (${window.location.href} includes ${p.url.replace('https://', '').replace('http://', '')}): ${urlMatch}`)

          return nameMatch || urlMatch
        }) || null

        if (this.currentPlatform) {
          console.log('【EchoSync】Current platform loaded:', this.currentPlatform)

          // 检查并更新favicon（如果需要）
          this.checkAndUpdateFavicon()
        } else {
          console.warn('【EchoSync】Current platform not found in database')
          console.warn('【EchoSync】Available platforms:', platformsResult.data.map(p => ({ name: p.name, url: p.url })))
        }
      } else {
        console.error('【EchoSync】Failed to load platforms from database:', platformsResult.error)
      }
    } catch (error) {
      console.error('【EchoSync】Error loading current platform:', error)
    }
  }

  /**
   * 检查并更新favicon（异步执行，不阻塞主流程）
   */
  private async checkAndUpdateFavicon(): Promise<void> {
    try {
      const platformInfo = this.currentPlatform ?
        `${this.currentPlatform.name}(ID:${this.currentPlatform.id})` :
        '未知平台'

      console.log(`【AIAdapter-${platformInfo}】准备检查favicon更新需求`)

      // 异步执行，不阻塞主流程
      setTimeout(async () => {
        try {
          console.log(`【AIAdapter-${platformInfo}】开始执行favicon检查`)

          // 清理缓存确保重新检查
          faviconManager.clearCache()
          console.log(`【AIAdapter-${platformInfo}】已清理favicon缓存`)

          const success = await faviconManager.checkAndUpdateFavicon()
          if (success) {
            console.log(`【AIAdapter-${platformInfo}】Favicon检查和更新完成`)
          } else {
            console.log(`【AIAdapter-${platformInfo}】Favicon检查完成，无需更新或更新失败`)
          }
        } catch (error) {
          console.warn(`【AIAdapter-${platformInfo}】Favicon更新过程中出现错误:`, error)
          // 不影响主要功能，只记录警告
        }
      }, 2000) // 延迟2秒执行，确保页面完全加载

    } catch (error) {
      console.warn('【AIAdapter】启动favicon检查失败:', error)
      // 不影响主要功能
    }
  }

  /**
   * 显示通知
   */
  protected showNotification(message: string, type: string = 'info'): void {
    // 创建简单的通知显示
    const notification = document.createElement('div')
    notification.textContent = message
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 12px 20px;
      background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
      color: white;
      border-radius: 8px;
      z-index: 10002;
      font-size: 14px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      transition: all 0.3s ease;
    `

    document.body.appendChild(notification)

    // 3秒后自动移除
    setTimeout(() => {
      notification.style.opacity = '0'
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification)
        }
      }, 300)
    }, 3000)
  }

  /**
   * 处理历史项点击
   */
  protected handleHistoryItemClick(chat: any): void {
    console.log('Handling history item click:', chat)
    this.injectPrompt(chat.chat_prompt)
  }

  /**
   * 获取当前输入内容 - 公开方法供外部调用
   */
  public getCurrentInput(): string {
    // 先尝试子类的自定义实现
    const customInput = this.customGetCurrentInput()
    if (customInput !== null) {
      return customInput
    }

    // 使用默认的InputManager实现
    return this.inputManager.getCurrentInput()
  }

  /**
   * 子类可重写的自定义输入获取方法
   * 返回null表示使用默认实现
   */
  protected customGetCurrentInput(): string | null {
    return null
  }

  /**
   * 获取平台名称
   */
  getPlatformName(): string {
    console.log('getPlatformName called, platform:', this.platform)
    return this.platform?.name || 'Unknown'
  }

  /**
   * 获取选择器
   */
  getSelectors(): { inputField: string; sendButton: string; messageContainer: string } {
    return this.selectors
  }

  /**
   * 设置业务逻辑 - 由content/index.ts调用
   */
  async setupBusinessLogic(): Promise<void> {
    console.log('【EchoSync】Setting up business logic for', this.platform.name)

    // 子类可以重写此方法添加额外的业务逻辑
    await this.setupCustomBusinessLogic()
  }

  /**
   * 子类可重写的自定义业务逻辑设置
   */
  protected async setupCustomBusinessLogic(): Promise<void> {
    // 默认为空，子类可以重写
  }

  /**
   * 处理发送前的提示词捕捉
   */
  private async handlePromptSend(prompt: string): Promise<void> {
    try {
      console.log('【EchoSync】Handling prompt send:', prompt)

      // 自动存档发送的提示词
      await this.archiveButton.autoArchivePrompt(prompt, this.currentPlatform)
    } catch (error) {
      console.error('【EchoSync】Handle prompt send error:', error)
    }
  }

  /**
   * 调试功能
   */
  protected debugFeatures(): void {
    console.group('【EchoSync】=== AI Adapter Debug Info ===')
    console.log('【EchoSync】Platform:', this.platform)
    console.log('【EchoSync】Current Platform:', this.currentPlatform)
    console.log('【EchoSync】Selectors:', this.selectors)
    console.log('【EchoSync】Input Element:', this.inputManager.getInputElement())
    console.log('【EchoSync】Floating Bubble:', this.floatingBubble.getBubble())
    console.log('【EchoSync】Archive Button:', this.archiveButton.getButton())
    console.groupEnd()
  }

  /**
   * 销毁适配器
   */
  destroy(): void {
    this.floatingBubble.destroy()
    this.archiveButton.destroy()
    this.dragHandler?.destroy()
    this.inputManager.destroy()
    this.historyManager.destroy()

    console.log('【EchoSync】AI Adapter destroyed')
  }
}
