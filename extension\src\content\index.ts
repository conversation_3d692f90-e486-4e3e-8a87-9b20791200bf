import { MessagingService } from '@/lib/service/messagingService'
import { MessageType, ChromeMessage, AIPlatform } from '@/types'
import { ChatGPTAdapter } from './adapters/chatgpt'
import { DeepSeekAdapter } from './adapters/deepseek'
import { ClaudeAdapter } from './adapters/claude'
import { GeminiAdapter } from './adapters/gemini'
import { KimiAdapter } from './adapters/kimi'
import { AIAdapter } from './base'

console.log('【EchoSync】EchoSync Content Script loaded on:', window.location.href)
console.log('【EchoSync】Current hostname:', window.location.hostname)
console.log('【EchoSync】User agent:', navigator.userAgent)

class ContentScriptManager {
  private adapter?: AIAdapter;
  private isInitialized: boolean = false;

  constructor() {
    this.adapter = null
    this.isInitialized = false
    this.init()
  }
  async init() {
    if (this.isInitialized) {
      console.log('【EchoSync】ContentScriptManager already initialized, skipping')
      return
    }

    console.log('【EchoSync】----------- ContentScriptManager initializing... -----------')

    // 检测当前平台并初始化适配器
    this.adapter = this.detectPlatform()

    if (this.adapter) {
      console.log('【EchoSync】Detected platform:', this.adapter.getPlatformName())

      // 初始化适配器的通用功能（悬浮气泡、存档按钮等）
      await this.adapter.initUniversalFeatures()
      console.log('【EchoSync】Adapter  features init success!')


      await this.setupEventListeners()
      console.log('【EchoSync】Event listeners init success!')

      this.isInitialized = true
      console.log('【EchoSync】----------- ContentScriptManager initialization complete! -----------')
    } else {
      console.log('【EchoSync】No platform adapter found, initialization skipped')
    }
  }

  // 检测当前页面所属的平台
  detectPlatform() {
    const hostname = window.location.hostname
    console.log('【EchoSync】Detecting platform for hostname:', hostname)

    // 使用正则表达式匹配各平台域名
    const platformPatterns = {
      chatgpt: /^chat\.openai\.com$/,
      deepseek: /^chat\.deepseek\.com$/,
      claude: /^claude\.ai$/,
      gemini: /^gemini\.google\.com$/,
      kimi: /^(.*\.)?(kimi\.moonshot\.cn|kimi\.com|www\.kimi\.com)$/
    }

    // 检查并返回对应的适配器
    if (platformPatterns.chatgpt.test(hostname)) {
      console.log('【EchoSync】Detected ChatGPT platform')
      return new ChatGPTAdapter()
    }
    if (platformPatterns.deepseek.test(hostname)) {
      console.log('【EchoSync】Detected DeepSeek platform')
      return new DeepSeekAdapter()
    }
    if (platformPatterns.claude.test(hostname)) {
      console.log('【EchoSync】Detected Claude platform')
      return new ClaudeAdapter()
    }
    if (platformPatterns.gemini.test(hostname)) {
      console.log('【EchoSync】Detected Gemini platform')
      return new GeminiAdapter()
    }
    if (platformPatterns.kimi.test(hostname)) {
      console.log('【EchoSync】Detected Kimi platform')
      return new KimiAdapter()
    }

    console.log('【EchoSync】No platform detected for hostname:', hostname)
    return null
  }

  // 设置事件监听器
  async setupEventListeners() {
    if (!this.adapter) return

    // 监听来自background的消息
    MessagingService.onMessage(async (message, sender, sendResponse) => {
      try {
        switch (message.type) {
          case MessageType.INJECT_PROMPT:
            await this.adapter.injectPrompt(message.payload.prompt)
            sendResponse({ success: true })
            break

          case MessageType.CAPTURE_PROMPT:
            const prompt = this.adapter.getCurrentInput()
            if (prompt) {
              await MessagingService.sendToBackground(MessageType.CAPTURE_PROMPT, {
                content: prompt,
                platform: this.adapter.getPlatformName().toLowerCase()
              })
            }
            sendResponse({ success: true, data: prompt })
            break

          default:
            sendResponse({ success: false, error: 'Unknown message type' })
        }
      } catch (error) {
        console.error('【EchoSync】Content script message handler error:', error)
        sendResponse({
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    })

    // 设置适配器的业务逻辑
    await this.adapter.setupBusinessLogic()
  }


}

// 等待DOM加载完成后初始化
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    new ContentScriptManager()
  })
} else {
  new ContentScriptManager()
}

// 处理SPA路由变化
let lastUrl = location.href
new MutationObserver(() => {
  const url = location.href
  if (url !== lastUrl) {
    lastUrl = url
    // 延迟重新初始化，等待页面渲染完成
    setTimeout(() => {
      new ContentScriptManager()
    }, 1000)
  }
}).observe(document, { subtree: true, childList: true })
