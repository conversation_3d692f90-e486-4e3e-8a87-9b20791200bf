import Dexie, { Table } from 'dexie'
import { ChatHistory, Platform, ChatPrompt } from '@/types/database_entity'

export interface ChatHistoryWithPlatform extends ChatHistory {
  platform_name: string
  platform_url: string
  platform_icon: string
  platform_icon_blob?: Blob
}

export class EchoSyncDatabase extends Dexie {
  // 定义表
  chatHistory!: Table<ChatHistory>
  chatPrompt!: Table<ChatPrompt>
  platform!: Table<Platform>

  constructor() {
    super('EchoSyncDatabase')
    
    // 定义数据库结构
    this.version(1).stores({
      chatHistory: '++id, chat_uid, platform_id, create_time, is_delete, is_synced',
      platform: '++id, name, url, is_delete'
    })

    // 添加索引以提高查询性能
    this.version(2).stores({
      chatHistory: '++id, chat_uid, platform_id, create_time, is_delete, is_synced, [platform_id+create_time]',
      platform: '++id, name, url, is_delete'
    }).upgrade(tx => {
      console.log('Upgrading database to version 2...')
    })

    // 添加chat_prompt索引以支持跨平台UID共享
    this.version(3).stores({
      chatHistory: '++id, chat_uid, platform_id, create_time, is_delete, is_synced, chat_prompt, [platform_id+create_time]',
      platform: '++id, name, url, is_delete'
    }).upgrade(tx => {
      console.log('Upgrading database to version 3 - adding chat_prompt index...')
    })

    // 添加icon_blob字段支持BLOB存储
    this.version(4).stores({
      chatHistory: '++id, chat_uid, platform_id, create_time, is_delete, is_synced, chat_prompt, [platform_id+create_time]',
      platform: '++id, name, url, is_delete'
    }).upgrade(tx => {
      console.log('Upgrading database to version 4 - adding icon_blob support...')
      // 注意：Dexie会自动处理新字段的添加，无需手动迁移
    })

    // 数据结构重构：分离chat_prompt表
    this.version(5).stores({
      chatHistory: '++id, chat_uid, platform_id, create_time, is_delete, is_synced, is_answered, [platform_id+create_time]',
      chatPrompt: '++id, chat_prompt, chat_uid, create_time, is_delete, is_synced',
      platform: '++id, name, url, is_delete'
    }).upgrade(async tx => {
      console.log('【EchoSync】Upgrading database to version 5 - restructuring chat data...')
      await this.migrateToVersion5(tx)
    })
  }

  /**
   * 数据迁移到版本5：分离chat_prompt表
   */
  private async migrateToVersion5(tx: any): Promise<void> {
    try {
      console.log('【EchoSync】Starting migration to version 5...')

      // 获取所有现有的聊天历史记录
      const existingChatHistories = await tx.table('chatHistory').toArray()
      console.log(`【EchoSync】Found ${existingChatHistories.length} existing chat history records`)

      // 提取唯一的提示词
      const uniquePromptsMap = new Map<string, {
        chat_prompt: string
        chat_uid: string
        earliest_time: number
      }>()

      for (const history of existingChatHistories) {
        if (history.chat_prompt) {
          const existing = uniquePromptsMap.get(history.chat_prompt)
          if (!existing || history.create_time < existing.earliest_time) {
            uniquePromptsMap.set(history.chat_prompt, {
              chat_prompt: history.chat_prompt,
              chat_uid: history.chat_uid,
              earliest_time: history.create_time
            })
          }
        }
      }

      console.log(`【EchoSync】Extracted ${uniquePromptsMap.size} unique prompts`)

      // 创建chat_prompt记录
      const chatPromptRecords = Array.from(uniquePromptsMap.values()).map(prompt => ({
        chat_prompt: prompt.chat_prompt,
        chat_uid: prompt.chat_uid,
        create_time: prompt.earliest_time,
        is_synced: 0,
        is_delete: 0
      }))

      // 批量插入chat_prompt记录
      await tx.table('chatPrompt').bulkAdd(chatPromptRecords)
      console.log(`【EchoSync】Inserted ${chatPromptRecords.length} chat prompt records`)

      // 更新chat_history记录，添加is_answered字段并移除chat_prompt字段
      const updatedChatHistories = existingChatHistories.map(history => {
        const { chat_prompt, ...historyWithoutPrompt } = history
        return {
          ...historyWithoutPrompt,
          is_answered: history.chat_answer ? 1 : 0
        }
      })

      // 清空并重新插入chat_history记录
      await tx.table('chatHistory').clear()
      await tx.table('chatHistory').bulkAdd(updatedChatHistories)
      console.log(`【EchoSync】Updated ${updatedChatHistories.length} chat history records`)

      console.log('【EchoSync】Migration to version 5 completed successfully')
    } catch (error) {
      console.error('【EchoSync】Migration to version 5 failed:', error)
      throw error
    }
  }

  /**
   * 初始化数据库
   */
  async initialize(): Promise<void> {
    try {
      console.log('【EchoSync】Starting database initialization...')
      await this.open()
      console.log('【EchoSync】Database opened successfully')

      await this.insertDefaultPlatforms()
      console.log('【EchoSync】Default platforms inserted')

      // 验证数据库是否正常工作
      const platformCount = await this.platform.count()
      const chatHistoryCount = await this.chatHistory.count()
      const chatPromptCount = await this.chatPrompt.count()
      console.log('【EchoSync】Database verification - Platforms:', platformCount, 'ChatHistory:', chatHistoryCount, 'ChatPrompt:', chatPromptCount)

      // 验证数据完整性
      await this.validateDataIntegrity()

      console.log('【EchoSync】Dexie database initialized successfully')
    } catch (error) {
      console.error('【EchoSync】Database initialization failed:', error)
      console.error('【EchoSync】Error details:', {
        name: error instanceof Error ? error.name : 'Unknown',
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : 'No stack trace'
      })
      throw error
    }
  }

  /**
   * 验证数据完整性
   */
  private async validateDataIntegrity(): Promise<void> {
    try {
      console.log('【EchoSync】Starting data integrity validation...')

      // 检查chat_uid关联关系
      const chatHistories = await this.chatHistory.toArray()
      const chatPrompts = await this.chatPrompt.toArray()

      const promptUids = new Set(chatPrompts.map(p => p.chat_uid))
      const historyUids = new Set(chatHistories.map(h => h.chat_uid))

      // 检查是否有孤立的历史记录（没有对应的提示词）
      const orphanedHistories = chatHistories.filter(h => !promptUids.has(h.chat_uid))
      if (orphanedHistories.length > 0) {
        console.warn(`【EchoSync】Found ${orphanedHistories.length} orphaned chat histories without corresponding prompts`)
      }

      // 检查是否有未使用的提示词（没有对应的历史记录）
      const unusedPrompts = chatPrompts.filter(p => !historyUids.has(p.chat_uid))
      if (unusedPrompts.length > 0) {
        console.warn(`【EchoSync】Found ${unusedPrompts.length} unused prompts without corresponding histories`)
      }

      // 检查chat_prompt表中是否有重复的提示词
      const promptTexts = chatPrompts.map(p => p.chat_prompt)
      const uniquePromptTexts = new Set(promptTexts)
      if (promptTexts.length !== uniquePromptTexts.size) {
        console.warn('【EchoSync】Found duplicate prompts in chat_prompt table')
      }

      console.log('【EchoSync】Data integrity validation completed')
    } catch (error) {
      console.error('【EchoSync】Data integrity validation failed:', error)
      // 不抛出错误，只记录警告
    }
  }

  /**
   * 插入默认平台数据
   */
  private async insertDefaultPlatforms(): Promise<void> {
    const defaultPlatforms: Platform[] = [
      {
        id: 1,
        name: 'DeepSeek',
        url: 'https://chat.deepseek.com',
        icon: 'https://chat.deepseek.com/favicon.ico',
        is_delete: 0
      },
      {
        id: 2,
        name: 'Kimi',
        url: 'https://kimi.moonshot.cn',
        icon: 'https://kimi.moonshot.cn/favicon.ico',
        is_delete: 0
      },
      {
        id: 3,
        name: 'ChatGPT',
        url: 'https://chat.openai.com',
        icon: 'https://chat.openai.com/favicon.ico',
        is_delete: 0
      },
      {
        id: 4,
        name: 'Claude',
        url: 'https://claude.ai',
        icon: 'https://claude.ai/favicon.ico',
        is_delete: 0
      },
      {
        id: 5,
        name: 'Gemini',
        url: 'https://gemini.google.com',
        icon: 'https://gemini.google.com/favicon.ico',
        is_delete: 0
      }
    ]

    // 逐个检查并插入平台数据，确保ID固定
    for (const platform of defaultPlatforms) {
      const existing = await this.platform.get(platform.id)
      if (!existing) {
        await this.platform.put(platform)
        console.log(`Platform ${platform.name} (ID: ${platform.id}) inserted`)
      }
    }
  }

  /**
   * 获取聊天历史（带平台信息）
   */
  async getChatHistoryWithPlatform(options: {
    limit?: number
    offset?: number
    platform_id?: number
    order_by?: 'create_time' | 'id'
    order_direction?: 'ASC' | 'DESC'
  } = {}): Promise<ChatHistoryWithPlatform[]> {
    const {
      limit = 50,
      offset = 0,
      platform_id,
      order_by = 'create_time',
      order_direction = 'DESC'
    } = options

    let query = this.chatHistory
      .where('is_delete')
      .equals(0)

    if (platform_id) {
      query = query.and(item => item.platform_id === platform_id)
    }

    const chatHistories = await query
      .offset(offset)
      .limit(limit)
      .reverse() // Dexie 中的倒序
      .toArray()

    // 获取平台信息并合并
    const platformIds = [...new Set(chatHistories.map(ch => ch.platform_id))]
    const platforms = await this.platform
      .where('id')
      .anyOf(platformIds)
      .toArray()

    const platformMap = new Map(platforms.map(p => [p.id!, p]))

    return chatHistories.map(ch => {
      const platform = platformMap.get(ch.platform_id)

      return {
        ...ch,
        platform_name: platform?.name || 'Unknown',
        platform_url: platform?.url || '',
        platform_icon: platform?.icon || '',
        platform_icon_base64: platform?.icon_base64
      }
    })
  }

  /**
   * 获取去重的聊天历史
   */
  async getUniqueChats(options: {
    limit?: number
    order_direction?: 'ASC' | 'DESC'
  } = {}): Promise<ChatHistoryWithPlatform[]> {
    const { limit = 20, order_direction = 'DESC' } = options

    // 获取所有未删除的聊天记录
    const allChats = await this.getChatHistoryWithPlatform({
      limit: 1000, // 先获取更多数据用于去重
      order_direction
    })

    // 按 chat_uid 去重，保留最新的
    const uniqueChatsMap = new Map<string, ChatHistoryWithPlatform>()
    
    for (const chat of allChats) {
      const existing = uniqueChatsMap.get(chat.chat_uid)
      if (!existing || chat.create_time > existing.create_time) {
        uniqueChatsMap.set(chat.chat_uid, chat)
      }
    }

    // 转换为数组并排序
    const uniqueChats = Array.from(uniqueChatsMap.values())
    uniqueChats.sort((a, b) => {
      return order_direction === 'DESC' 
        ? b.create_time - a.create_time
        : a.create_time - b.create_time
    })

    return uniqueChats.slice(0, limit)
  }

  /**
   * 全文搜索聊天历史
   */
  async searchChatHistory(searchTerm: string, options: {
    limit?: number
    platform_id?: number
  } = {}): Promise<ChatHistoryWithPlatform[]> {
    const { limit = 50, platform_id } = options

    // Dexie 不支持全文搜索，使用简单的文本匹配
    let query = this.chatHistory
      .where('is_delete')
      .equals(0)

    if (platform_id) {
      query = query.and(item => item.platform_id === platform_id)
    }

    const results = await query
      .filter(item => 
        item.chat_prompt.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (item.chat_answer && item.chat_answer.toLowerCase().includes(searchTerm.toLowerCase()))
      )
      .limit(limit)
      .toArray()

    // 获取平台信息并合并
    const platformIds = [...new Set(results.map(ch => ch.platform_id))]
    const platforms = await this.platform
      .where('id')
      .anyOf(platformIds)
      .toArray()

    const platformMap = new Map(platforms.map(p => [p.id!, p]))

    return results.map(ch => {
      const platform = platformMap.get(ch.platform_id)
      return {
        ...ch,
        platform_name: platform?.name || 'Unknown',
        platform_url: platform?.url || '',
        platform_icon: platform?.icon || '',
        platform_icon_base64: platform?.icon_base64
      }
    })
  }

  /**
   * 获取提示词列表（带平台统计）
   */
  async getPromptsWithPlatforms(options: {
    limit?: number
    offset?: number
    order_by?: 'create_time' | 'id'
    order_direction?: 'ASC' | 'DESC'
  } = {}): Promise<Array<{
    id: number
    chat_prompt: string
    chat_uid: string
    create_time: number
    platforms: Array<{
      platform_id: number
      platform_name: string
      platform_icon?: string
      platform_icon_base64?: string
      count: number
      latest_time: number
    }>
  }>> {
    const {
      limit = 50,
      offset = 0,
      order_by = 'create_time',
      order_direction = 'DESC'
    } = options

    // 获取提示词列表
    let promptQuery = this.chatPrompt.where('is_delete').equals(0)
    const prompts = await promptQuery
      .offset(offset)
      .limit(limit)
      .reverse() // Dexie 中的倒序
      .toArray()

    // 获取每个提示词对应的平台统计
    const result = []
    for (const prompt of prompts) {
      const histories = await this.chatHistory
        .where('chat_uid')
        .equals(prompt.chat_uid)
        .and(item => item.is_delete === 0)
        .toArray()

      // 按平台统计
      const platformStats = new Map<number, {
        platform_id: number
        count: number
        latest_time: number
      }>()

      for (const history of histories) {
        const existing = platformStats.get(history.platform_id)
        if (!existing || history.create_time > existing.latest_time) {
          platformStats.set(history.platform_id, {
            platform_id: history.platform_id,
            count: (existing?.count || 0) + 1,
            latest_time: Math.max(history.create_time, existing?.latest_time || 0)
          })
        } else {
          platformStats.set(history.platform_id, {
            ...existing,
            count: existing.count + 1
          })
        }
      }

      // 获取平台信息
      const platformIds = Array.from(platformStats.keys())
      const platforms = await this.platform
        .where('id')
        .anyOf(platformIds)
        .toArray()

      const platformMap = new Map(platforms.map(p => [p.id!, p]))

      const platformsWithStats = Array.from(platformStats.values()).map(stat => {
        const platform = platformMap.get(stat.platform_id)
        return {
          platform_id: stat.platform_id,
          platform_name: platform?.name || 'Unknown',
          platform_icon: platform?.icon,
          platform_icon_base64: platform?.icon_base64,
          count: stat.count,
          latest_time: stat.latest_time
        }
      })

      result.push({
        id: prompt.id!,
        chat_prompt: prompt.chat_prompt,
        chat_uid: prompt.chat_uid,
        create_time: prompt.create_time,
        platforms: platformsWithStats
      })
    }

    return result
  }

  /**
   * 获取提示词详情（包含所有历史记录）
   */
  async getChatPromptDetail(chatUid: string): Promise<{
    prompt: ChatPrompt | null
    histories: ChatHistoryWithPlatform[]
  }> {
    // 获取提示词
    const prompt = await this.chatPrompt
      .where('chat_uid')
      .equals(chatUid)
      .and(item => item.is_delete === 0)
      .first()

    if (!prompt) {
      return { prompt: null, histories: [] }
    }

    // 获取历史记录
    const histories = await this.chatHistory
      .where('chat_uid')
      .equals(chatUid)
      .and(item => item.is_delete === 0)
      .toArray()

    // 获取平台信息并合并
    const platformIds = [...new Set(histories.map(h => h.platform_id))]
    const platforms = await this.platform
      .where('id')
      .anyOf(platformIds)
      .toArray()

    const platformMap = new Map(platforms.map(p => [p.id!, p]))

    const historiesWithPlatform = histories.map(h => {
      const platform = platformMap.get(h.platform_id)
      return {
        ...h,
        platform_name: platform?.name || 'Unknown',
        platform_url: platform?.url || '',
        platform_icon: platform?.icon || '',
        platform_icon_base64: platform?.icon_base64
      }
    })

    return {
      prompt,
      histories: historiesWithPlatform
    }
  }
}

// 导出单例实例
export const dexieDatabase = new EchoSyncDatabase()
